package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@ToString
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class CaisseEmpruntHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "caisse_emprunt_id")
    private CaisseEmprunt caisseEmprunt;
    
    private Double amount;
    private LocalDateTime dateEmprunt;
    private LocalDateTime dateRemboursement;
    private String type;

    @CreationTimestamp
    @Column(updatable = false)
    private LocalDateTime creationDate;

    @UpdateTimestamp
    private LocalDateTime updateDate;
}
