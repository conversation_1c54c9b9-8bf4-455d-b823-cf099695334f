package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.referentiel.ActionStatusDTO;
import ma.almobadara.backend.enumeration.BeneficiaryStatus;
import ma.almobadara.backend.model.administration.SousZone;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaireDonorBeneficiary;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import ma.almobadara.backend.util.strings.HandleSpecialChars;
import org.hibernate.annotations.BatchSize;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Slf4j
@Entity
@Getter
@Setter
@EqualsAndHashCode(exclude = {"person", "documents", "notes", "beneficiaryServices", "educations", "scholarshipBeneficiaries", "epsResidents", "diseaseTreatments", "beneficiaryDiseases", "beneficiaryAllergies", "handicapped", "glasses"}, callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@BatchSize(size = 16)
public class Beneficiary extends BaseEntity{

    private Boolean independent;
    private Boolean epsResident;
    private Boolean archived;
    @Column(unique = true)
    private String code;
    private String addedYear;
    private String accountingCode;
    @OneToMany(targetEntity = NoteBeneficiary.class, mappedBy = "beneficiary")
    private List<NoteBeneficiary> notes;
    @OneToMany(targetEntity = DocumentBeneficiary.class, mappedBy = "beneficiary")
    private List<DocumentBeneficiary> documents;
    @OneToMany(targetEntity = BeneficiaryService.class, mappedBy = "beneficiary")
    private List<BeneficiaryService> beneficiaryServices;
    @OneToMany(targetEntity = Education.class, mappedBy = "beneficiary")
    private List<Education> educations;
    @OneToMany(targetEntity = ScholarshipBeneficiary.class, mappedBy = "beneficiary")
    private List<ScholarshipBeneficiary> scholarshipBeneficiaries;
    @OneToMany(targetEntity = EpsResident.class, mappedBy = "beneficiary")
    private List<EpsResident> epsResidents;
    @OneToMany(targetEntity = DiseaseTreatment.class, mappedBy = "beneficiary")
    private List<DiseaseTreatment> diseaseTreatments;
    @OneToMany(targetEntity = BeneficiaryHandicap.class, mappedBy = "beneficiary")
    private List<BeneficiaryHandicap> handicapped;
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "person_id")
    private Person person;
    @OneToOne(mappedBy = "beneficiary", fetch = FetchType.LAZY)
    private Glasses glasses;
    @OneToMany(targetEntity = BeneficiaryDisease.class, mappedBy = "beneficiary")
    private List<BeneficiaryDisease> beneficiaryDiseases;
    @OneToMany(targetEntity = TakenInChargeBeneficiary.class, mappedBy = "beneficiary")
    private List<TakenInChargeBeneficiary> takenInChargeBeneficiaries;
    @OneToMany(targetEntity = BeneficiaryAllergy.class, mappedBy = "beneficiary")
    private List<BeneficiaryAllergy> beneficiaryAllergies;
    @Transient
    private ActionStatusDTO actionStatus;
    private Long actionStatusId;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "zone_id")
    private Zone zone;
    // New fields
    private String remarqueFr;
    private String remarqueEn;
    private String remarqueAr;
    private Long typeKafalatId;
    //private String typePriseEnChargeIds;
    private Long sourceBeneficiaryId;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "beneficiary_statut_id")
    private BeneficiaryStatut beneficiaryStatut;
    @OneToMany(targetEntity = HistoryBeneficiary.class, mappedBy = "beneficiary")
    private List<HistoryBeneficiary> historyBeneficiaries;
    private String rqComplete ;
    private String rqReject;
    @OneToMany(mappedBy = "beneficiary", cascade = CascadeType.ALL)
    private List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries;
    @ManyToMany
    @JoinTable(
            name = "beneficiary_aide_complementaire",
            joinColumns = @JoinColumn(name = "beneficiary_id"),
            inverseJoinColumns = @JoinColumn(name = "aide_complementaire_id")
    )
    private List<AideComplementaire> aideComplementaires;
    private String statusBeneficiaryAdHoc;
    private String comment;
    private String coordinates;


    @ManyToMany
    @JoinTable(
            name = "beneficiary_adhocgroup", // Nom de la table d'association
            joinColumns = @JoinColumn(name = "beneficiary_id"), // Clé étrangère pour Beneficiary
            inverseJoinColumns = @JoinColumn(name = "beneficiary_ad_hoc_group_id") // Clé étrangère pour BeneficiaryAdHocGroup
    )
    private List<BeneficiaryAdHocGroup> beneficiaryAdHocGroups;
    private String codeBeneficiary;


    private LocalDateTime lastUpdateDate;

    // add the sousZone id
    @ManyToOne
    @JoinColumn(name = "sous_zone_id")
    private SousZone sousZone;

    private Boolean hasRapport;
    private Boolean oldBeneficiary;

    public void checkAndUpdateStatus() {
        if (this.beneficiaryStatut != null && this.beneficiaryStatut.getId().equals(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId())) {
            if (this.lastUpdateDate != null && ChronoUnit.YEARS.between(this.lastUpdateDate, LocalDateTime.now()) >= 1) {
                ////ici je veux loguer un message
                log.info("hello from checkAndUpdateStatus");
                this.beneficiaryStatut = new BeneficiaryStatut(BeneficiaryStatus.CANDIDAT_A_UPDATER.getId());
            }
        }
    }

    public String getAudit(Map<String, String> params,StringBuilder familleInfomations,List<String> aideComplementaires) {
        DateFormat format=new SimpleDateFormat("dd/MM/yyyy");
        String date=format.format(person.getBirthDate());
        return "{"
                + "\"Type de Bénéficiaire\": \"" + (independent ? "Indépendant" : "Membre famille") + "\","
                +(!independent
                ?"\"Famille\": \"" + escapeSpecialChars(familleInfomations != null ?familleInfomations.toString():"-") + "\",":"")
                + "\"Nature de Bénéficiaire\": \"" + (oldBeneficiary ? "Ancien Bénéficiaire" : "Nouvelle Bénéficiaire") + "\","
                + (oldBeneficiary
                ? "\"Code Comptable\": \"" + escapeSpecialChars(accountingCode) + "\","
                + "\"Code de Candidature\": \"" + escapeSpecialChars(codeBeneficiary) + "\","
                : "")
                + "\"Nom\": \"" + escapeSpecialChars(person.getLastName()) + "\","
                + "\"Prénom\": \"" + escapeSpecialChars(person.getFirstName()) + "\","
                + "\"Nom Arabe\": \"" + escapeSpecialChars(person.getLastNameAr()) + "\","
                + "\"Prénom Arabe\": \"" + escapeSpecialChars(person.getFirstNameAr()) + "\","
                + "\"Date de naissance\": \"" + escapeSpecialChars(date) + "\","
                + "\"Type d'identité\": \"" + escapeSpecialChars(params.get("typeIdentity")) + "\","
                + "\"Code d'identité\": \"" + escapeSpecialChars(person.getIdentityCode()) + "\","
                + "\"Sexe\": \"" + escapeSpecialChars(person.getSex()) + "\","
                + "\"Téléphone personnel\": \"" + escapeSpecialChars(person.getPhoneNumber()) + "\","
                + "\"Type d'hébergement Personnel\": \"" + escapeSpecialChars(params.get("accommodationType")) + "\","
                + "\"Nature d'hébergement Personnel\": \"" + escapeSpecialChars(params.get("accommodationNature")) + "\","
                + "\"Profession\": \"" + escapeSpecialChars(params.get("profession")) + "\","
                + "\"Email\": \"" + escapeSpecialChars(person.getEmail()) + "\","
                + "\"Source bénéficiaire\": \"" + escapeSpecialChars(params.get("sourceBeneficiaira")) + "\","
                + "\"Catégorie\": \"" + escapeSpecialChars(params.get("category")) + "\","
                + "\"Zone\": \"" + escapeSpecialChars(zone != null ? zone.getName() : "-") + "\","
                + "\"Sous-zone\": \"" + escapeSpecialChars(sousZone != null ? sousZone.getName() : "-") + "\","
                + "\"Type Kafalat\": \"" + escapeSpecialChars(params.get("typeKafalat")) + "\","
                + "\"Date de dépôt de la candidature\": \"" + escapeSpecialChars(addedYear) + "\","
                + "\"Aides complémentaires prévues\": \"" +
                (aideComplementaires != null
                        ? aideComplementaires.stream()
                        .map(HandleSpecialChars::escapeSpecialChars) // Use the actual class name
                        .collect(Collectors.joining(", "))
                        : "-")
                + "\","
                + "\"Remarques et recommandations\": \"" + escapeSpecialChars(remarqueFr) + "\","
                + "\"Code\": \"" + escapeSpecialChars(code) + "\""
                + "}";
    }


    public String getAuditConsultation(String rq) {
        return "{"
                + "\"Nom\": \"" + escapeSpecialChars(person.getFirstName() + " - " + person.getLastName()) + "\","
                + "\"Code\": \"" + escapeSpecialChars(code) + "\","
                + "\"Status\": \"" + escapeSpecialChars(beneficiaryStatut.getNameStatut()) + "\""
                + (rq != null ? ",\"Remarque\": \"" + escapeSpecialChars(rq) + "\"" : "")
                + ",\"Zone\": \"" + escapeSpecialChars(zone !=null? zone.getName():null) + "\""
                + "}";
    }



}
